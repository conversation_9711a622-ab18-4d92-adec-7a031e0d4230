<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Token Expiration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3730a3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار انتهاء صلاحية الرمز المميز</h1>
        <p>هذه الصفحة تختبر وظيفة تسجيل الخروج التلقائي عند انتهاء صلاحية الجلسة</p>

        <div class="test-section">
            <h3>حالة المصادقة الحالية</h3>
            <div id="auth-status"></div>
            <button onclick="checkAuthStatus()">فحص حالة المصادقة</button>
        </div>

        <div class="test-section">
            <h3>محاكاة رمز منتهي الصلاحية</h3>
            <p>هذا سيضع رمز مميز منتهي الصلاحية في التخزين المحلي</p>
            <button onclick="setExpiredToken()">وضع رمز منتهي الصلاحية</button>
            <button onclick="testApiCall()">اختبار استدعاء API</button>
        </div>

        <div class="test-section">
            <h3>اختبار استدعاءات API</h3>
            <button onclick="testFetchActivities()">اختبار جلب النشاطات</button>
            <button onclick="testUploadFile()">اختبار رفع ملف</button>
            <button onclick="testUpdateProfile()">اختبار تحديث الملف الشخصي</button>
        </div>

        <div class="test-section">
            <h3>إعادة تعيين</h3>
            <button onclick="clearTokens()">مسح جميع الرموز</button>
            <button onclick="setValidToken()">وضع رمز صالح</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('ndyt_token');
            const user = localStorage.getItem('ndyt_user');
            const teamPin = localStorage.getItem('ndyt_team_pin');
            
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = `
                <div class="status info">
                    <strong>الرمز المميز:</strong> ${token ? 'موجود' : 'غير موجود'}<br>
                    <strong>المستخدم:</strong> ${user ? 'موجود' : 'غير موجود'}<br>
                    <strong>رقم الفريق:</strong> ${teamPin ? 'موجود' : 'غير موجود'}
                </div>
            `;
        }

        function setExpiredToken() {
            // Set an obviously expired JWT token
            const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJ0ZXN0IiwiZXhwIjoxNjAwMDAwMDAwfQ.invalid';
            localStorage.setItem('ndyt_token', expiredToken);
            localStorage.setItem('ndyt_user', JSON.stringify({id: 1, username: 'test'}));
            localStorage.setItem('ndyt_team_pin', '1234');
            log('تم وضع رمز منتهي الصلاحية', 'info');
            checkAuthStatus();
        }

        function setValidToken() {
            // This would normally be a valid token from login
            localStorage.setItem('ndyt_token', 'valid_token_here');
            localStorage.setItem('ndyt_user', JSON.stringify({id: 1, username: 'test'}));
            localStorage.setItem('ndyt_team_pin', '1234');
            log('تم وضع رمز صالح (وهمي)', 'success');
            checkAuthStatus();
        }

        function clearTokens() {
            localStorage.removeItem('ndyt_token');
            localStorage.removeItem('ndyt_user');
            localStorage.removeItem('ndyt_team_pin');
            log('تم مسح جميع الرموز', 'info');
            checkAuthStatus();
        }

        async function testApiCall() {
            const token = localStorage.getItem('ndyt_token');
            if (!token) {
                log('لا يوجد رمز مميز للاختبار', 'error');
                return;
            }

            try {
                log('جاري اختبار استدعاء API...', 'info');
                const response = await fetch('/api/v1/ndyt-activities/activities', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401 || response.status === 403) {
                    log('تم اكتشاف رمز منتهي الصلاحية! يجب أن يتم تسجيل الخروج التلقائي الآن', 'error');
                    // Simulate what the API service should do
                    localStorage.removeItem('ndyt_token');
                    localStorage.removeItem('ndyt_user');
                    localStorage.removeItem('ndyt_team_pin');
                    log('تم تسجيل الخروج التلقائي', 'success');
                    checkAuthStatus();
                } else if (response.ok) {
                    log('استدعاء API نجح', 'success');
                } else {
                    log(`استدعاء API فشل مع الحالة: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`خطأ في استدعاء API: ${error.message}`, 'error');
            }
        }

        async function testFetchActivities() {
            log('اختبار جلب النشاطات...', 'info');
            await testApiCall();
        }

        async function testUploadFile() {
            log('اختبار رفع ملف...', 'info');
            const token = localStorage.getItem('ndyt_token');
            if (!token) {
                log('لا يوجد رمز مميز للاختبار', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', new Blob(['test'], {type: 'text/plain'}), 'test.txt');
                
                const response = await fetch('/api/v1/ndyt-activities/upload-file', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (response.status === 401 || response.status === 403) {
                    log('تم اكتشاف رمز منتهي الصلاحية في رفع الملف!', 'error');
                } else {
                    log(`استجابة رفع الملف: ${response.status}`, 'info');
                }
            } catch (error) {
                log(`خطأ في رفع الملف: ${error.message}`, 'error');
            }
        }

        async function testUpdateProfile() {
            log('اختبار تحديث الملف الشخصي...', 'info');
            const token = localStorage.getItem('ndyt_token');
            if (!token) {
                log('لا يوجد رمز مميز للاختبار', 'error');
                return;
            }

            try {
                const response = await fetch('/api/v1/ndyt-activities/user/update-profile', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        full_name: 'Test User',
                        team_pin: '1234'
                    })
                });

                if (response.status === 401 || response.status === 403) {
                    log('تم اكتشاف رمز منتهي الصلاحية في تحديث الملف الشخصي!', 'error');
                } else {
                    log(`استجابة تحديث الملف الشخصي: ${response.status}`, 'info');
                }
            } catch (error) {
                log(`خطأ في تحديث الملف الشخصي: ${error.message}`, 'error');
            }
        }

        // Initialize
        checkAuthStatus();
        log('تم تحميل صفحة اختبار انتهاء صلاحية الرمز المميز', 'success');
    </script>
</body>
</html>
