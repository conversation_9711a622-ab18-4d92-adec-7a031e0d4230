[{"F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\main.js": "1", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\App.vue": "2", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\router\\index.js": "3", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\LoginView.vue": "4", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\RegisterView.vue": "5", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\ActivityView.vue": "6", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\AdminView.vue": "7", "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\services\\api.js": "8"}, {"size": 4826, "mtime": 1756428230125, "results": "9", "hashOfConfig": "10"}, {"size": 2917, "mtime": 1756319459000, "results": "11", "hashOfConfig": "10"}, {"size": 2839, "mtime": 1756428455372, "results": "12", "hashOfConfig": "10"}, {"size": 9442, "mtime": 1756319459000, "results": "13", "hashOfConfig": "10"}, {"size": 9328, "mtime": 1756319459000, "results": "14", "hashOfConfig": "10"}, {"size": 196724, "mtime": 1756428421392, "results": "15", "hashOfConfig": "10"}, {"size": 15950, "mtime": 1756428437502, "results": "16", "hashOfConfig": "10"}, {"size": 3264, "mtime": 1756428251473, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "20"}, "exc11s", {"filePath": "21", "messages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "23"}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "36", "usedDeprecatedRules": "20"}, "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\main.js", [], [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\App.vue", [], [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\router\\index.js", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\LoginView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\RegisterView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\ActivityView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\AdminView.vue", [], "F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\services\\api.js", ["37", "38"], "import router from '@/router'\n\nclass ApiService {\n  constructor() {\n    this.baseURL = '/api/v1/ndyt-activities'\n  }\n\n  async request(endpoint, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const teamPin = localStorage.getItem('ndyt_team_pin')\n    \n    const config = {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    }\n\n    // Add authorization header if token exists\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`\n    }\n\n    // Add team pin header if it exists\n    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {\n      config.headers['x-team-pin'] = teamPin\n    }\n\n    try {\n      const response = await fetch(`${this.baseURL}${endpoint}`, config)\n      \n      // Handle token expiration globally\n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration()\n        throw new Error('Session expired')\n      }\n\n      return response\n    } catch (error) {\n      // Re-throw the error so components can handle it\n      throw error\n    }\n  }\n\n  handleTokenExpiration() {\n    // Clear all authentication data\n    localStorage.removeItem('ndyt_token')\n    localStorage.removeItem('ndyt_user')\n    localStorage.removeItem('ndyt_team_pin')\n\n    // Show toast notification\n    try {\n      // Try to use Vue's toast if available\n      const app = document.getElementById('app')?.__vue_app__\n      if (app && app.config.globalProperties.$toast) {\n        app.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى')\n      } else {\n        // Fallback to console log\n        console.warn('Session expired. Redirecting to login.')\n      }\n    } catch (error) {\n      console.warn('Session expired. Redirecting to login.')\n    }\n\n    // Redirect to login page\n    router.push('/login')\n  }\n\n  // Convenience methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'GET' })\n  }\n\n  async post(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'POST',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async put(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'DELETE' })\n  }\n\n  // File upload method\n  async uploadFile(file, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const config = {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`\n      },\n      body: formData,\n      ...options\n    }\n\n    try {\n      const response = await fetch(`${this.baseURL}/upload-file`, config)\n      \n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration()\n        throw new Error('Session expired')\n      }\n\n      return response\n    } catch (error) {\n      throw error\n    }\n  }\n}\n\nexport default new ApiService()\n", {"ruleId": "39", "severity": 2, "message": "40", "line": 31, "column": 5, "nodeType": "41", "messageId": "42", "endLine": 44, "endColumn": 6}, {"ruleId": "39", "severity": 2, "message": "40", "line": 111, "column": 5, "nodeType": "41", "messageId": "42", "endLine": 122, "endColumn": 6}, "no-useless-catch", "Unnecessary try/catch wrapper.", "TryStatement", "unnecessaryCatch"]