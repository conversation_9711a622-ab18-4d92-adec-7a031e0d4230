import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Swal from 'sweetalert2'
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'
import apiService from './services/api'

// Aggressive cache busting
const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);

// Clear only non-authentication storage before app initialization
if (typeof Storage !== 'undefined') {
  // Preserve authentication tokens
  const ndytToken = localStorage.getItem('ndyt_token');
  const ndytUser = localStorage.getItem('ndyt_user');
  const ndytTeamPin = localStorage.getItem('ndyt_team_pin');
  
  // Clear all storage
  localStorage.clear();
  sessionStorage.clear();
  
  // Restore authentication data
  if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);
  if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);
  if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);
}

// Add cache-busting meta tags
const addCacheBustingMeta = () => {
  const metaTags = [
    { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },
    { name: 'pragma', content: 'no-cache' },
    { name: 'expires', content: '0' },
    { name: 'last-modified', content: new Date().toUTCString() },
    { name: 'etag', content: cacheBuster },
    { name: 'cache-buster', content: cacheBuster }
  ];
  
  metaTags.forEach(tag => {
    const meta = document.createElement('meta');
    meta.setAttribute('http-equiv', tag.name);
    meta.setAttribute('content', tag.content);
    document.head.appendChild(meta);
  });
};

// Force reload on browser back/forward
window.addEventListener('pageshow', (event) => {
  if (event.persisted || window.performance.navigation.type === 2) {
    window.location.reload(true);
  }
});

// Prevent caching on unload but preserve auth tokens
window.addEventListener('beforeunload', () => {
  if (typeof Storage !== 'undefined') {
    // Preserve authentication tokens
    const ndytToken = localStorage.getItem('ndyt_token');
    const ndytUser = localStorage.getItem('ndyt_user');
    const ndytTeamPin = localStorage.getItem('ndyt_team_pin');
    
    // Clear all storage
    localStorage.clear();
    sessionStorage.clear();
    
    // Restore authentication data
    if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);
    if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);
    if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);
  }
});

const app = createApp(App)

// Configure SweetAlert2 with app colors
const swalConfig = {
  background: '#1e1e2e',
  color: '#f1f5f9',
  confirmButtonColor: '#4f46e5',
  cancelButtonColor: '#e53e3e',
  customClass: {
    popup: 'swal-popup',
    confirmButton: 'swal-confirm-btn',
    cancelButton: 'swal-cancel-btn'
  }
};

// Configure Toast with app colors
const toastOptions = {
  position: 'top-right',
  timeout: 4000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: true,
  toastDefaults: {
    success: {
      toastClassName: 'toast-success',
      bodyClassName: 'toast-body',
      hideProgressBar: false,
      timeout: 3000
    },
    error: {
      toastClassName: 'toast-error', 
      bodyClassName: 'toast-body',
      hideProgressBar: false,
      timeout: 5000
    }
  }
};

// Make SweetAlert2 globally available
app.config.globalProperties.$swal = Swal.mixin(swalConfig);

// Make API service globally available
app.config.globalProperties.$api = apiService;

// Add global cache-busting mixin
app.mixin({
  beforeCreate() {
    // Force component refresh
    this.$options._cacheBuster = cacheBuster;
  },
  mounted() {
    // Add cache-busting to component
    if (this.$el && this.$el.setAttribute) {
      this.$el.setAttribute('data-cache-bust', cacheBuster);
    }
  }
});

app.use(Toast, toastOptions)

// Fallback shim: ensure this.$toast is always available
if (!app.config.globalProperties.$toast || typeof app.config.globalProperties.$toast.success !== 'function') {
  const swalInstance = app.config.globalProperties.$swal || Swal.mixin(swalConfig);
  app.config.globalProperties.$toast = {
    success: (message) => swalInstance.fire({ title: 'تم بنجاح', text: message, icon: 'success' }),
    error: (message) => swalInstance.fire({ title: 'حدث خطأ', text: message, icon: 'error' }),
    info: (message) => swalInstance.fire({ title: 'إشعار', text: message, icon: 'info' }),
    warning: (message) => swalInstance.fire({ title: 'تنبيه', text: message, icon: 'warning' })
  };
}

app.use(router)

// Add cache-busting meta tags before mounting
addCacheBustingMeta();

app.mount('#app')
